/**
 * Admin Jobs API Endpoint
 * Provides admin-specific job management and monitoring capabilities
 * Acts as a wrapper around the automation jobs system with admin authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { getJobQueue, getJobManager } from '@/lib/jobs';
import { JobType, JobPriority, JobStatus } from '@/lib/jobs/types';
import { log } from '@/lib/logging/logger';

/**
 * GET /api/admin/jobs
 * Get jobs with admin authentication and filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const toolId = searchParams.get('toolId');
    const search = searchParams.get('search');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Use enhanced job system for better data and monitoring
    const jobManager = getJobManager();
    let jobs = await jobManager.getJobs({
      status: status as JobStatus,
      type: type as JobType,
      limit: limit + offset, // Get more to handle pagination
      offset: 0
    });

    // Apply additional filtering
    if (toolId) {
      jobs = jobs.filter(job => job.toolId === toolId);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      jobs = jobs.filter(job => 
        job.toolName?.toLowerCase().includes(searchLower) ||
        job.url?.toLowerCase().includes(searchLower) ||
        job.id.toLowerCase().includes(searchLower)
      );
    }

    if (startDate) {
      const start = new Date(startDate);
      jobs = jobs.filter(job => new Date(job.createdAt) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      jobs = jobs.filter(job => new Date(job.createdAt) <= end);
    }

    // Apply pagination
    const paginatedJobs = jobs.slice(offset, offset + limit);

    // Transform jobs for admin interface compatibility
    const transformedJobs = paginatedJobs.map(job => ({
      id: job.id,
      toolId: job.toolId || job.metadata?.toolId || `tool-${job.id}`,
      toolName: job.toolName || job.metadata?.toolName || `Job ${job.id}`,
      url: job.url || job.metadata?.url || 'https://example.com',
      status: job.status,
      priority: job.priority || 'medium',
      progress: job.progress || (job.status === 'completed' ? 100 :
               job.status === 'active' ? 50 : 0),
      createdAt: job.createdAt,
      startedAt: job.startedAt,
      completedAt: job.completedAt,
      estimatedTime: job.estimatedTime ||
                    (job.status === 'completed' ? '0 min' :
                     job.status === 'failed' ? 'Failed' : '5 min'),
      retryCount: job.retryCount || 0,
      maxRetries: job.maxRetries || 3,
      errorMessage: job.failedReason || job.error,
      type: job.type,
      metadata: job.metadata
    }));

    // Log admin access
    log.admin('jobs_accessed', `Admin accessed jobs list with filters`, {
      status,
      type,
      limit,
      offset,
      toolId,
      search,
      resultCount: transformedJobs.length
    });

    return NextResponse.json({
      success: true,
      data: transformedJobs,
      pagination: {
        total: jobs.length,
        limit,
        offset,
        hasMore: offset + limit < jobs.length
      }
    });

  } catch (error) {
    console.error('Admin jobs API error:', error);
    log.error('admin_jobs_api_error', 'Error in admin jobs API', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch jobs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/jobs
 * Create a new job with admin privileges
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, toolId, url, priority = 'medium', metadata = {} } = body;

    // Validate required fields
    if (!type || !toolId) {
      return NextResponse.json(
        { success: false, error: 'type and toolId are required' },
        { status: 400 }
      );
    }

    // Use job manager to create job
    const jobManager = getJobManager();
    const job = await jobManager.createJob({
      type: type as JobType,
      toolId,
      url,
      priority: priority as JobPriority,
      metadata: {
        ...metadata,
        createdBy: 'admin',
        adminCreated: true
      }
    });

    // Log admin job creation
    log.admin('job_created', `Admin created new job`, {
      jobId: job.id,
      type,
      toolId,
      priority
    });

    return NextResponse.json({
      success: true,
      data: {
        id: job.id,
        status: job.status,
        createdAt: job.createdAt,
        type: job.type,
        toolId: job.toolId,
        priority: job.priority
      }
    });

  } catch (error) {
    console.error('Admin job creation error:', error);
    log.error('admin_job_creation_error', 'Error creating job via admin API', { error });

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create job',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/jobs
 * Bulk delete jobs with admin privileges
 */
export async function DELETE(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { jobIds, status } = body;

    if (!jobIds && !status) {
      return NextResponse.json(
        { success: false, error: 'Either jobIds array or status filter is required' },
        { status: 400 }
      );
    }

    const jobManager = getJobManager();
    let deletedCount = 0;

    if (jobIds && Array.isArray(jobIds)) {
      // Delete specific jobs
      for (const jobId of jobIds) {
        try {
          await jobManager.deleteJob(jobId);
          deletedCount++;
        } catch (error) {
          console.error(`Failed to delete job ${jobId}:`, error);
        }
      }
    } else if (status) {
      // Delete jobs by status (e.g., all completed jobs)
      const jobs = await jobManager.getJobs({ status: status as JobStatus });
      for (const job of jobs) {
        try {
          await jobManager.deleteJob(job.id);
          deletedCount++;
        } catch (error) {
          console.error(`Failed to delete job ${job.id}:`, error);
        }
      }
    }

    // Log admin bulk deletion
    log.admin('jobs_bulk_deleted', `Admin deleted ${deletedCount} jobs`, {
      deletedCount,
      jobIds: jobIds || [],
      status
    });

    return NextResponse.json({
      success: true,
      data: {
        deletedCount,
        message: `Successfully deleted ${deletedCount} job${deletedCount !== 1 ? 's' : ''}`
      }
    });

  } catch (error) {
    console.error('Admin bulk job deletion error:', error);
    log.error('admin_bulk_job_deletion_error', 'Error in bulk job deletion via admin API', { error });

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete jobs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
